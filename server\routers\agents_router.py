"""
企业级智能体路由器

基于重构后的智能体系统，提供完整的权限控制、用户上下文管理和企业级功能。
集成统一数据库管理、权限框架和知识库系统。
"""

import asyncio
import json
import uuid
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig

from src.utils.logging_config import logger
from server.auth.rbac_middleware import get_required_user, get_admin_user
from server.models.user_model import User
from src.agents.context import UserContext
from src.agents.dependencies import get_agent_dependencies
from langchain_core.messages import AIMessageChunk
from src.models import select_model
from src import executor

# 创建智能体路由器
agents = APIRouter(prefix="/agents", tags=["智能体管理"])

# ==================== 请求响应模型 ====================

class AgentExecuteRequest(BaseModel):
    """智能体执行请求"""
    agent_name: str = Field(..., description="智能体名称")
    messages: List[Dict[str, str]] = Field(..., description="消息列表")
    config: Optional[Dict[str, Any]] = Field(None, description="运行时配置")
    stream: bool = Field(False, description="是否流式返回")
    stream_mode: str = Field("messages", description="流模式: messages 或 values")
    tools: Optional[List[str]] = Field(None, description="指定要使用的工具列表")

class AgentConfigRequest(BaseModel):
    """智能体配置请求"""
    model: Optional[str] = Field(None, description="模型名称")
    system_prompt: Optional[str] = Field(None, description="系统提示词")
    temperature: Optional[float] = Field(None, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大令牌数")
    tools: Optional[List[str]] = Field(None, description="工具列表")

class AgentInfoResponse(BaseModel):
    """智能体信息响应"""
    name: str
    description: str
    status: str
    permissions: Optional[Dict[str, bool]] = None
    available_tools: Optional[List[str]] = None

class ClearCacheRequest(BaseModel):
    """清除缓存请求"""
    cache_type: str = Field("all", description="缓存类型: all, instances, tools")
    user_id: Optional[str] = Field(None, description="目标用户ID（管理员可用）")

# ==================== 智能体信息API ====================

@agents.get("/info")
async def get_agents_info(
    current_user: User = Depends(get_required_user)
) -> List[AgentInfoResponse]:
    """
    获取智能体信息 - 权限过滤
    
    返回用户有权限访问的智能体列表，包含权限信息和可用工具。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)
        
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 获取智能体信息（自动权限过滤）
        agents_info = await agent_manager.get_agents_info(user_context)
        
        # 转换为响应格式
        response = []
        for agent_info in agents_info:
            response.append(AgentInfoResponse(
                name=agent_info.get("name", "unknown"),
                description=agent_info.get("description", ""),
                status=agent_info.get("status", "unknown"),
                permissions=agent_info.get("permissions", {}),
                available_tools=agent_info.get("available_tools", []),
                config=agent_info.get("config", {})
            ))
        
        logger.info(f"用户 {current_user.id} 获取智能体信息，返回 {len(response)} 个智能体")
        return response
        
    except Exception as e:
        logger.error(f"获取智能体信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取智能体信息失败: {str(e)}")

@agents.get("/{agent_name}/info")
async def get_agent_info(
    agent_name: str,
    current_user: User = Depends(get_required_user)
) -> AgentInfoResponse:
    """
    获取特定智能体信息
    
    返回指定智能体的详细信息，需要访问权限。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)
        
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 获取智能体实例（自动权限检查）
        agent = await agent_manager.get_agent(agent_name, user_context)
        agent_info = await agent.get_info()
        
        # 获取用户可用工具
        available_tools = await agent_manager.get_available_tools(user_context)
        
        return AgentInfoResponse(
            name=agent_info.get("name", agent_name),
            description=agent_info.get("description", ""),
            status=agent_info.get("status", "ready"),
            permissions={
                "access": True,  # 能获取到说明有访问权限
                "execute": await agent_manager._check_agent_permission(user_context, agent_name, "execute")
            },
            available_tools=list(available_tools.keys()),
            config=agent_info.get("config", {})
        )
        
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取智能体 {agent_name} 信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取智能体信息失败: {str(e)}")

# ==================== 智能体执行API ====================

@agents.post("/chat")
async def execute_agent(
    request: AgentExecuteRequest,
    current_user: User = Depends(get_required_user)
):
    """
    执行智能体 - 企业级权限控制

    使用旧架构的模型加载逻辑作为主要方案，保持权限隔离和工具过滤功能。
    支持流式和非流式响应，自动进行权限检查。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)

        # 基础权限检查 - 检查用户是否有权限使用智能体
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        agent_manager = await dependencies.agent_manager

        # 检查智能体访问权限
        if not await agent_manager._check_agent_permission(user_context, request.agent_name, "execute"):
            raise PermissionError(f"用户无权执行智能体: {request.agent_name}")

        # 使用旧架构的模型加载逻辑（主要方案）
        config = request.config or {}

        # 确定模型提供商和模型名称
        model_provider = None
        model_name = None

        if "model" in config and "/" in str(config["model"]):
            # 新格式: "provider/model_name"
            parts = str(config["model"]).split("/", 1)
            model_provider = parts[0]
            model_name = parts[1] if len(parts) > 1 else None
        elif "model_provider" in config:
            # 旧格式
            model_provider = config["model_provider"]
            model_name = config.get("model_name")
        else:
            # 使用默认配置
            from src import config as sys_config
            model_provider = sys_config.model_provider
            model_name = sys_config.model_name

        # 使用旧架构加载模型
        model = select_model(model_provider=model_provider, model_name=model_name)
        logger.info(f"加载模型: {model_provider}/{model_name}, 类型: {type(model)}")

        # 获取用户可用工具（保持权限隔离）
        available_tools = await agent_manager.get_available_tools(user_context)
        logger.info(f"用户 {current_user.id} 获取到可用工具: {list(available_tools.keys())}")
        
        # 如果用户指定了工具，则过滤可用工具
        if request.tools:
            # 只保留用户指定且有权限的工具
            filtered_tools = {}
            for tool_name in request.tools:
                if tool_name in available_tools:
                    filtered_tools[tool_name] = available_tools[tool_name]
            available_tools = filtered_tools
            logger.info(f"用户 {current_user.id} 过滤后的工具: {list(available_tools.keys())}")

        # 构建消息内容
        if request.messages:
            # 取最后一条用户消息
            last_message = request.messages[-1]
            if isinstance(last_message, dict):
                query = last_message.get("content", "")
            else:
                query = str(last_message)
        else:
            query = "Hello"

        # 构建元数据
        meta = {
            "query": query,
            "agent_name": request.agent_name,
            "model_provider": model_provider,
            "model_name": model_name,
            "thread_id": config.get("thread_id") or str(uuid.uuid4()),
            "user_id": str(current_user.id),
            "available_tools_count": len(available_tools)
        }

        # 优先尝试使用智能体系统，失败时降级到模型直接调用
        try:
            if len(available_tools) > 0:
                # 有工具时使用智能体系统
                logger.info(f"使用智能体系统执行 {request.agent_name}，工具数量: {len(available_tools)}")
                agent = await agent_manager.get_agent(request.agent_name, user_context)
                
                # 构建智能体执行配置
                agent_config = RunnableConfig(
                    configurable={
                        "thread_id": meta["thread_id"],
                        "user_context": user_context,
                        "filtered_tools": available_tools,  # 传递过滤后的工具
                        **config
                    }
                )

                # 构建消息
                from langchain_core.messages import HumanMessage
                messages = [HumanMessage(content=query)]

                if request.stream:
                    # 流式响应
                    return StreamingResponse(
                        _stream_agent_response(agent, messages, agent_config, meta, request, current_user),
                        media_type='application/json'
                    )
                else:
                    # 非流式响应
                    graph = await agent.get_graph(config=agent_config, user_context=user_context)
                    result = await graph.ainvoke(
                        {"messages": messages},
                        config=agent_config
                    )
                    
                    # 提取最后的消息
                    last_message = result["messages"][-1] if result.get("messages") else None
                    response_content = last_message.content if last_message else "没有响应"

                    return {
                        "success": True,
                        "agent_name": request.agent_name,
                        "result": {
                            "messages": [{"role": "assistant", "content": response_content}]
                        },
                        "meta": meta,
                        "mode": "agent_execution",
                        "tools_used": len(available_tools)
                    }
            else:
                # 无工具时直接使用模型
                raise ValueError("无可用工具，使用模型直接调用")
                
        except Exception as agent_error:
            # 降级到模型直接调用
            logger.warning(f"智能体执行失败，降级到模型直接调用: {agent_error}")
            
            if request.stream:
                # 流式响应
                return StreamingResponse(
                    _stream_model_response(model, query, meta, request, current_user),
                    media_type='application/json'
                )
            else:
                # 非流式响应
                async def predict_async(query):
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(executor, model.predict, query)

                response = await predict_async(query)

                return {
                    "success": True,
                    "agent_name": request.agent_name,
                    "result": {
                        "messages": [{"role": "assistant", "content": response.content}]
                    },
                    "meta": meta,
                    "mode": "legacy_model_fallback",
                    "tools_available": len(available_tools)
                }

    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"执行智能体 {request.agent_name} 失败: {e}")
        # 提供更详细的错误信息
        if "unexpected keyword argument" in str(e):
            raise HTTPException(
                status_code=400,
                detail=f"配置参数错误: {str(e)}. 请检查配置字段是否正确."
            )
        else:
            raise HTTPException(status_code=500, detail=f"智能体执行失败: {str(e)}")

async def _stream_agent_response(agent, messages, agent_config, meta: Dict[str, Any], request: AgentExecuteRequest, current_user: User):
    """
    智能体的流式响应生成器

    使用真正的智能体系统进行流式对话，支持工具调用。
    """
    def make_chunk(**kwargs):
        return json.dumps(kwargs, ensure_ascii=False).encode('utf-8') + b"\n"

    try:
        # 发送初始状态
        yield make_chunk(
            status="init",
            agent_name=request.agent_name,
            user_id=str(current_user.id),
            meta=meta,
            mode="agent_execution"
        )

        # 获取智能体图
        user_context = agent_config["configurable"]["user_context"]
        graph = await agent.get_graph(config=agent_config, user_context=user_context)
        
        logger.info(f"开始流式执行智能体 {request.agent_name}")

        # 流式执行智能体
        async for event in graph.astream(
            {"messages": messages}, 
            config=agent_config,
            stream_mode=request.stream_mode
        ):
            logger.debug(f"智能体事件: {type(event)} - {str(event)[:200]}")
            
            # 处理不同类型的事件
            if isinstance(event, dict):
                # 处理节点事件
                for node_name, node_data in event.items():
                    if node_name == "chatbot" and "messages" in node_data:
                        # LLM响应事件
                        for msg in node_data["messages"]:
                            if hasattr(msg, 'content') and msg.content:
                                yield make_chunk(
                                    status="streaming",
                                    content=msg.content,
                                    chunk_type="llm_response",
                                    node=node_name
                                )
                    elif node_name == "tools" and "messages" in node_data:
                        # 工具调用事件
                        for msg in node_data["messages"]:
                            yield make_chunk(
                                status="streaming",
                                content=f"[工具调用: {getattr(msg, 'name', 'unknown')}]",
                                chunk_type="tool_call",
                                node=node_name
                            )
            elif isinstance(event, tuple):
                # 处理元组事件，通常包含 AIMessageChunk
                # 遍历元组中的每个元素
                for item in event:
                    if hasattr(item, 'content') and item.content:
                        yield make_chunk(
                            status="streaming",
                            content=item.content,
                            chunk_type="ai_message_chunk"
                        )
            elif hasattr(event, 'content') and event.content:
                # 直接处理 AIMessageChunk 类型的事件
                yield make_chunk(
                    status="streaming",
                    content=event.content,
                    chunk_type="direct_chunk"
                )

        # 发送完成状态
        yield make_chunk(status="finished", meta=meta)

    except Exception as e:
        logger.error(f"智能体流式响应失败: {e}")
        yield make_chunk(
            status="error",
            error=str(e)
        )

async def _stream_model_response(model, query: str, meta: Dict[str, Any], request: AgentExecuteRequest, current_user: User):
    """
    旧架构模型的流式响应生成器

    使用旧架构的模型进行流式对话，保持与 chat_router.py 相似的逻辑。
    """
    def make_chunk(**kwargs):
        return json.dumps(kwargs, ensure_ascii=False).encode('utf-8') + b"\n"

    try:
        # 发送初始状态
        yield make_chunk(
            status="init",
            agent_name=request.agent_name,
            user_id=str(current_user.id),
            meta=meta,
            mode="legacy_model_loading"
        )

        # 流式生成响应
        try:
            for chunk in model._stream_response([{"role": "user", "content": query}]):
                if hasattr(chunk, 'content') and chunk.content:
                    yield make_chunk(
                        status="streaming",
                        content=chunk.content,
                        chunk_type="model_stream"
                    )
        except Exception as stream_error:
            # 如果流式失败，降级到非流式
            logger.warning(f"流式处理失败，降级到非流式: {stream_error}")
            response = model._get_response([{"role": "user", "content": query}])
            yield make_chunk(
                status="streaming",
                content=response.content,
                chunk_type="model_fallback"
            )

        # 发送完成状态
        yield make_chunk(status="finished", meta=meta)

    except Exception as e:
        logger.error(f"模型流式响应失败: {e}")
        yield make_chunk(
            status="error",
            error=str(e)
        )



# ==================== 工具管理API ====================

@agents.get("/tools/available")
async def get_available_tools(
    current_user: User = Depends(get_required_user)
) -> Dict[str, List[str]]:
    """
    获取用户可用工具
    
    返回用户有权限使用的所有工具，按类别分组。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)
        
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 获取用户可用工具
        available_tools = await agent_manager.get_available_tools(user_context)
        
        # 按类别分组
        categorized_tools = {
            "basic": [],
            "knowledge": [],
            "web": [],
            "other": []
        }
        
        for tool_name in available_tools.keys():
            if tool_name in ["calculator", "query_knowledge_graph"]:
                categorized_tools["basic"].append(tool_name)
            elif tool_name.startswith("retrieve_"):
                categorized_tools["knowledge"].append(tool_name)
            elif "web" in tool_name.lower() or "search" in tool_name.lower():
                categorized_tools["web"].append(tool_name)
            else:
                categorized_tools["other"].append(tool_name)
        
        logger.info(f"用户 {current_user.id} 获取可用工具，共 {len(available_tools)} 个")
        return categorized_tools
        
    except Exception as e:
        logger.error(f"获取用户可用工具失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取可用工具失败: {str(e)}")

# ==================== 配置管理API ====================

@agents.get("/{agent_name}/config")
async def get_agent_config(
    agent_name: str,
    current_user: User = Depends(get_required_user)
) -> Dict[str, Any]:
    """
    获取智能体配置
    
    返回用户对该智能体的个性化配置。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)
        
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 获取智能体实例（自动权限检查）
        agent = await agent_manager.get_agent(agent_name, user_context)
        
        # 获取配置
        config = await agent.config_schema.from_runnable_config(
            config={},
            agent_name=agent_name,
            user_context=user_context
        )
        
        logger.info(f"用户 {current_user.id} 获取智能体 {agent_name} 配置")
        return config.__dict__ if hasattr(config, '__dict__') else config
        
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取智能体 {agent_name} 配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@agents.put("/{agent_name}/config")
async def update_agent_config(
    agent_name: str,
    config: AgentConfigRequest,
    current_user: User = Depends(get_admin_user)  # 需要管理员权限
) -> Dict[str, Any]:
    """
    更新智能体配置
    
    管理员专用，更新智能体的全局配置。
    """
    try:
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 获取智能体实例
        user_context = UserContext.from_user(current_user)
        agent = await agent_manager.get_agent(agent_name, user_context)
        
        # 更新配置
        config_dict = config.model_dump(exclude_none=True)
        result = agent.config_schema.save_to_file(config_dict, agent_name)
        
        if result:
            logger.info(f"管理员 {current_user.id} 更新智能体 {agent_name} 配置")
            return {
                "success": True,
                "message": f"智能体 {agent_name} 配置已更新",
                "config": config_dict
            }
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")
        
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"更新智能体 {agent_name} 配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

# ==================== 缓存管理API ====================

@agents.post("/cache/clear")
async def clear_cache(
    request: ClearCacheRequest,
    current_user: User = Depends(get_required_user)
) -> Dict[str, Any]:
    """
    清除用户缓存
    
    普通用户只能清除自己的缓存，管理员可以清除指定用户的缓存。
    """
    try:
        # 确定目标用户
        target_user_id = request.user_id
        if target_user_id and target_user_id != current_user.id:
            # 需要管理员权限才能清除其他用户缓存
            if not current_user.is_admin:
                raise HTTPException(status_code=403, detail="无权限清除其他用户缓存")
        else:
            target_user_id = current_user.id
        
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        await dependencies.ensure_initialized()
        
        agent_manager = await dependencies.agent_manager
        
        # 清除缓存
        if request.cache_type in ["all", "instances"]:
            agent_manager.clear_user_cache(target_user_id)
        
        if request.cache_type in ["all", "tools"]:
            tools_factory = agent_manager._tools_factory
            if tools_factory:
                tools_factory.clear_cache(target_user_id)
        
        logger.info(f"用户 {current_user.id} 清除了用户 {target_user_id} 的 {request.cache_type} 缓存")
        return {
            "success": True,
            "message": f"已清除用户 {target_user_id} 的 {request.cache_type} 缓存"
        }
        
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

# ==================== 系统管理API ====================

@agents.get("/health")
async def health_check(
    current_user: User = Depends(get_required_user)
) -> Dict[str, Any]:
    """
    智能体系统健康检查
    
    返回智能体系统的健康状态和统计信息。
    """
    try:
        # 获取智能体管理器
        dependencies = get_agent_dependencies()
        
        # 基础健康检查
        health_status = await dependencies.health_check()
        
        # 智能体管理器健康检查
        if dependencies._agent_manager:
            agent_manager = await dependencies.agent_manager
            agent_health = await agent_manager.health_check()
            health_status["agent_manager"] = agent_health
        
        logger.debug(f"用户 {current_user.id} 请求智能体系统健康检查")
        return health_status
        
    except Exception as e:
        logger.error(f"智能体系统健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@agents.post("/reinitialize")
async def reinitialize_agents(
    current_user: User = Depends(get_admin_user)  # 需要管理员权限
) -> Dict[str, Any]:
    """
    重新初始化智能体管理器
    
    管理员专用，用于系统维护和故障恢复。
    """
    try:
        # 获取依赖管理器
        dependencies = get_agent_dependencies()
        
        # 重新初始化
        await dependencies.initialize_all()
        
        # 获取新状态
        health_status = await dependencies.health_check()
        
        logger.info(f"管理员 {current_user.id} 重新初始化智能体系统")
        return {
            "success": True,
            "message": "智能体系统已重新初始化",
            "health": health_status
        }
        
    except Exception as e:
        logger.error(f"重新初始化智能体系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"重新初始化失败: {str(e)}")

# ==================== 根路由 ====================

@agents.get("/")
async def agents_root(
    current_user: User = Depends(get_required_user)
) -> Dict[str, Any]:
    """
    智能体系统根路由
    
    返回系统基本信息和用户权限概要。
    """
    try:
        # 创建用户上下文
        user_context = UserContext.from_user_sync(current_user)
        
        # 获取基本信息
        dependencies = get_agent_dependencies()
        health = await dependencies.health_check()
        
        return {
            "system": "Enterprise Agent System",
            "version": "2.0.0",
            "user": {
                "id": user_context.user_id,
                "username": user_context.username,
                "roles": user_context.roles,
                "permissions_count": len(user_context.permissions)
            },
            "status": health.get("overall", False),
            "message": "企业级智能体系统已就绪"
        }
        
    except Exception as e:
        logger.error(f"获取智能体系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"系统信息获取失败: {str(e)}")

# ==================== 路由器导出 ====================

# 为了兼容性，同时导出 agents 和 router
router = agents